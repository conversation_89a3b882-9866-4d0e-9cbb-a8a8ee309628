import 'package:flutter/material.dart';
import 'package:echoverse/models/verse.dart';
import 'package:echoverse/models/user.dart';
import 'package:echoverse/services/verse_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/widgets/verse_card.dart';
import 'package:echoverse/widgets/common/loading_state.dart';
import 'package:echoverse/widgets/common/empty_state.dart';
import 'package:echoverse/widgets/common/app_buttons.dart';
import 'package:echoverse/design_system/app_spacing.dart';
import 'package:echoverse/screens/karaoke_screen.dart';
import 'package:echoverse/screens/progress_screen.dart';
import 'package:echoverse/screens/bible_books_screen.dart';

class HomeTab extends StatefulWidget {
  final SettingsService settingsService;
  final ProgressService progressService;
  final VerseService verseService;
  final UserService userService;

  const HomeTab({
    super.key,
    required this.settingsService,
    required this.progressService,
    required this.verseService,
    required this.userService,
  });

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  List<Verse> _verses = [];
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    final currentUser = await widget.userService.getCurrentUser();
    setState(() {
      _verses = widget.verseService.getAllVerses();
      _currentUser = currentUser;
      _isLoading = false;
    });
  }

  void _navigateToKaraoke(Verse verse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: verse,
          userId: _currentUser!.id,
          progressService: widget.progressService,
          settingsService: widget.settingsService,
        ),
      ),
    ).then((_) => setState(() {}));
  }

  void _navigateToProgress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProgressScreen(
          userId: _currentUser!.id,
          progressService: widget.progressService,
          verseService: widget.verseService,
        ),
      ),
    );
  }

  Widget _buildStatsCard(String title, String value, IconData icon, Color color, BuildContext context) {
    final theme = Theme.of(context);
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.12),
              color.withValues(alpha: 0.06),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, size: 24, color: color),
            ),
            const SizedBox(height: 16),
            TweenAnimationBuilder<int>(
              tween: IntTween(begin: 0, end: int.tryParse(value) ?? 0),
              duration: const Duration(milliseconds: 1200),
              curve: Curves.easeOutCubic,
              builder: (context, animatedValue, child) {
                return Text(
                  animatedValue.toString(),
                  style: theme.textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                    letterSpacing: -0.5,
                    height: 1.0,
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w600,
                letterSpacing: 0.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.colorScheme.surface,
        body: const LoadingState(message: 'Loading your verses...'),
      );
    }

    if (_currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('Please sign in')),
      );
    }

    final masteredCount = widget.progressService.getMasteredVerses(_currentUser!.id).length;
    final totalPractice = widget.progressService.getTotalPracticeCount(_currentUser!.id);
    final totalVerses = _verses.length;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Enhanced Header
            Padding(
              padding: const EdgeInsets.fromLTRB(AppSpacing.screenPadding, AppSpacing.xl, AppSpacing.screenPadding, AppSpacing.lg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'VerseFlow',
                              style: theme.textTheme.headlineLarge?.copyWith(
                                fontWeight: FontWeight.w800,
                                letterSpacing: -0.8,
                                height: 1.1,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Master Scripture through practice',
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                                letterSpacing: 0.1,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Daily streak indicator
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              theme.colorScheme.primary.withValues(alpha: 0.1),
                              theme.colorScheme.secondary.withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: theme.colorScheme.primary.withValues(alpha: 0.2),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.local_fire_department,
                              color: theme.colorScheme.primary,
                              size: 18,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '3', // This would be dynamic based on actual streak
                              style: theme.textTheme.labelLarge?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Stats Dashboard
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                children: [
                  _buildStatsCard(
                    'Mastered',
                    '$masteredCount',
                    Icons.check_circle,
                    theme.colorScheme.secondary,
                    context,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  _buildStatsCard(
                    'Practiced',
                    '$totalPractice',
                    Icons.repeat,
                    theme.colorScheme.primary,
                    context,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  _buildStatsCard(
                    'Total',
                    '$totalVerses',
                    Icons.menu_book,
                    theme.colorScheme.tertiary,
                    context,
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Daily Goal Progress
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: _buildDailyGoalCard(theme),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Enhanced Quick Actions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                children: [
                  Expanded(
                    child: _buildEnhancedQuickAction(
                      icon: Icons.menu_book_outlined,
                      label: 'Browse Bible',
                      subtitle: 'Find new verses',
                      color: theme.colorScheme.primary,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BibleBooksScreen(
                              progressService: widget.progressService,
                              userService: widget.userService,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: _buildEnhancedQuickAction(
                      icon: Icons.analytics_outlined,
                      label: 'View Progress',
                      subtitle: 'Track your growth',
                      color: theme.colorScheme.secondary,
                      onTap: _navigateToProgress,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppSpacing.lg),

            // Continue Learning Section
            if (_verses.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
                child: _buildContinueLearningSection(theme),
              ),
            
            const SizedBox(height: AppSpacing.lg),

            // Verses List Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'My Verses',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_verses.isEmpty)
                    Text(
                      'Add verses from Bible',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                ],
              ),
            ),
            
            const SizedBox(height: AppSpacing.md),

            // Verses List
            Expanded(
              child: _verses.isEmpty
                  ? EmptyState.noVerses(
                      onBrowseBible: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BibleBooksScreen(
                              progressService: widget.progressService,
                              userService: widget.userService,
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.fromLTRB(AppSpacing.screenPadding, 0, AppSpacing.screenPadding, AppSpacing.screenPadding),
                      itemCount: _verses.length,
                      itemBuilder: (context, index) {
                        final verse = _verses[index];
                        final progress = widget.progressService.getProgressForVerse(_currentUser!.id, verse.id);
                        return VerseCard(
                          verse: verse,
                          progress: progress,
                          onTap: () => _navigateToKaraoke(verse),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyGoalCard(ThemeData theme) {
    final dailyGoal = 3; // This would be configurable
    final todayPractice = 2; // This would be calculated from today's practice
    final progress = todayPractice / dailyGoal;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            theme.colorScheme.secondaryContainer.withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withValues(alpha: 0.06),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.track_changes,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Daily Goal',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    Text(
                      '$todayPractice of $dailyGoal verses practiced today',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${(progress * 100).round()}%',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Stack(
            children: [
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              TweenAnimationBuilder<double>(
                tween: Tween(begin: 0.0, end: progress.clamp(0.0, 1.0)),
                duration: const Duration(milliseconds: 1500),
                curve: Curves.easeOutCubic,
                builder: (context, animatedProgress, child) {
                  return FractionallySizedBox(
                    widthFactor: animatedProgress,
                    child: Container(
                      height: 8,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            theme.colorScheme.primary,
                            theme.colorScheme.secondary,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }


}

