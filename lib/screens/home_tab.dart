import 'package:flutter/material.dart';
import 'package:echoverse/models/verse.dart';
import 'package:echoverse/models/user.dart';
import 'package:echoverse/services/verse_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/settings_service.dart';
import 'package:echoverse/widgets/verse_card.dart';
import 'package:echoverse/widgets/common/loading_state.dart';
import 'package:echoverse/widgets/common/empty_state.dart';
import 'package:echoverse/widgets/common/app_buttons.dart';
import 'package:echoverse/design_system/app_spacing.dart';
import 'package:echoverse/screens/karaoke_screen.dart';
import 'package:echoverse/screens/progress_screen.dart';
import 'package:echoverse/screens/bible_books_screen.dart';

class HomeTab extends StatefulWidget {
  final SettingsService settingsService;
  final ProgressService progressService;
  final VerseService verseService;
  final UserService userService;

  const HomeTab({
    super.key,
    required this.settingsService,
    required this.progressService,
    required this.verseService,
    required this.userService,
  });

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  List<Verse> _verses = [];
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    final currentUser = await widget.userService.getCurrentUser();
    setState(() {
      _verses = widget.verseService.getAllVerses();
      _currentUser = currentUser;
      _isLoading = false;
    });
  }

  void _navigateToKaraoke(Verse verse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => KaraokeScreen(
          verse: verse,
          userId: _currentUser!.id,
          progressService: widget.progressService,
          settingsService: widget.settingsService,
        ),
      ),
    ).then((_) => setState(() {}));
  }

  void _navigateToProgress() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProgressScreen(
          userId: _currentUser!.id,
          progressService: widget.progressService,
          verseService: widget.verseService,
        ),
      ),
    );
  }

  Widget _buildStatsCard(String title, String value, IconData icon, Color color, BuildContext context) {
    final theme = Theme.of(context);
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 20, color: color),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.colorScheme.surface,
        body: const LoadingState(message: 'Loading your verses...'),
      );
    }

    if (_currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('Please sign in')),
      );
    }

    final masteredCount = widget.progressService.getMasteredVerses(_currentUser!.id).length;
    final totalPractice = widget.progressService.getTotalPracticeCount(_currentUser!.id);
    final totalVerses = _verses.length;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(AppSpacing.screenPadding, AppSpacing.lg, AppSpacing.screenPadding, AppSpacing.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'VerseFlow',
                    style: theme.textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    'Master Scripture through practice',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.65),
                      letterSpacing: 0.2,
                    ),
                  ),
                ],
              ),
            ),
            
            // Stats Dashboard
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                children: [
                  _buildStatsCard(
                    'Mastered',
                    '$masteredCount',
                    Icons.check_circle,
                    theme.colorScheme.secondary,
                    context,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  _buildStatsCard(
                    'Practiced',
                    '$totalPractice',
                    Icons.repeat,
                    theme.colorScheme.primary,
                    context,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  _buildStatsCard(
                    'Total',
                    '$totalVerses',
                    Icons.menu_book,
                    theme.colorScheme.tertiary,
                    context,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppSpacing.lg),

            // Quick Actions
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                children: [
                  Expanded(
                    child: QuickActionButton(
                      icon: Icons.menu_book_outlined,
                      label: 'Browse Bible',
                      color: theme.colorScheme.primary,
                      onTap: () {
                        // Note: Navigation to Bible tab would be handled by parent
                        // For now, navigate to Bible screen
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BibleBooksScreen(
                              progressService: widget.progressService,
                              userService: widget.userService,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: QuickActionButton(
                      icon: Icons.analytics_outlined,
                      label: 'View Progress',
                      color: theme.colorScheme.secondary,
                      onTap: _navigateToProgress,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppSpacing.lg),

            // Verses List Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSpacing.screenPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'My Verses',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_verses.isEmpty)
                    Text(
                      'Add verses from Bible',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                ],
              ),
            ),
            
            const SizedBox(height: AppSpacing.md),

            // Verses List
            Expanded(
              child: _verses.isEmpty
                  ? EmptyState.noVerses(
                      onBrowseBible: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BibleBooksScreen(
                              progressService: widget.progressService,
                              userService: widget.userService,
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.fromLTRB(AppSpacing.screenPadding, 0, AppSpacing.screenPadding, AppSpacing.screenPadding),
                      itemCount: _verses.length,
                      itemBuilder: (context, index) {
                        final verse = _verses[index];
                        final progress = widget.progressService.getProgressForVerse(_currentUser!.id, verse.id);
                        return VerseCard(
                          verse: verse,
                          progress: progress,
                          onTap: () => _navigateToKaraoke(verse),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }


}

