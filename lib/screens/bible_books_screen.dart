import 'dart:async';
import 'package:flutter/material.dart';
import 'package:echoverse/models/bible_book.dart';
import 'package:echoverse/models/user.dart';
import 'package:echoverse/services/bible_service.dart';
import 'package:echoverse/services/progress_service.dart';
import 'package:echoverse/services/user_service.dart';
import 'package:echoverse/screens/bible_chapters_screen.dart';
import 'package:echoverse/screens/bible_reader_screen.dart';
import 'package:echoverse/utils/storage_helper.dart';
import 'package:echoverse/design_system/app_spacing.dart';

class BibleBooksScreen extends StatefulWidget {
  final ProgressService? progressService;
  final UserService? userService;

  const BibleBooksScreen({
    super.key,
    this.progressService,
    this.userService,
  });

  @override
  State<BibleBooksScreen> createState() => _BibleBooksScreenState();
}

class _BibleBooksScreenState extends State<BibleBooksScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final BibleService _bibleService = BibleService();
  final TextEditingController _searchController = TextEditingController();
  List<BibleBook> _oldTestamentBooks = [];
  List<BibleBook> _newTestamentBooks = [];
  List<BibleBook> _filteredOldTestamentBooks = [];
  List<BibleBook> _filteredNewTestamentBooks = [];
  bool _isLoading = true;
  bool _isSearching = false;
  String? _error;
  String _searchQuery = '';
  late TabController _tabController;
  bool _hasRestoredPosition = false;
  User? _currentUser;
  Map<int, int> _bookProgressCounts = {}; // bookId -> number of verses practiced
  Timer? _searchDebounceTimer;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadBooks();
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _searchDebounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadBooks() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // This will trigger database download if needed
      final oldTestament = await _bibleService.getOldTestamentBooks();
      final newTestament = await _bibleService.getNewTestamentBooks();

      setState(() {
        _oldTestamentBooks = oldTestament;
        _newTestamentBooks = newTestament;
        _filteredOldTestamentBooks = oldTestament;
        _filteredNewTestamentBooks = newTestament;
        _isLoading = false;
      });

      // After loading books, check if we should restore last position
      await _restoreLastPosition();

      // Load progress data if services are available
      await _loadProgressData();
    } catch (e) {
      String errorMessage = 'Failed to load Bible books';

      // Provide more user-friendly error messages
      if (e.toString().contains('download') || e.toString().contains('network')) {
        errorMessage = 'Unable to download Bible database. Please check your internet connection and try again.';
      } else if (e.toString().contains('database') || e.toString().contains('SQLite')) {
        errorMessage = 'Database error. The Bible database may be corrupted. Try restarting the app.';
      } else {
        errorMessage = 'Error: ${e.toString()}';
      }

      setState(() {
        _error = errorMessage;
        _isLoading = false;
      });
    }
  }

  Future<void> _restoreLastPosition() async {
    // Only restore once per app session
    if (_hasRestoredPosition) return;

    final lastLocation = await StorageHelper.loadLastBibleLocation();
    if (lastLocation == null || !mounted) return;

    final bookId = lastLocation['bookId']!;
    final chapter = lastLocation['chapter']!;

    // Find the book in either testament
    final allBooks = [..._oldTestamentBooks, ..._newTestamentBooks];
    final book = allBooks.firstWhere(
      (b) => b.id == bookId,
      orElse: () => allBooks.first,
    );

    _hasRestoredPosition = true;

    // Navigate directly to the reader with the last chapter
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BibleReaderScreen(book: book, chapter: chapter),
      ),
    );
  }

  void _navigateToChapters(BibleBook book) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BibleChaptersScreen(book: book),
      ),
    );
  }

  void _onSearchChanged(String query) {
    // Cancel previous timer
    _searchDebounceTimer?.cancel();

    // Set immediate state for query
    setState(() {
      _searchQuery = query;
      _isSearching = query.isNotEmpty;
    });

    // Debounce the actual filtering
    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performSearch(query);
    });
  }

  void _performSearch(String query) {
    if (!mounted) return;

    setState(() {
      if (query.isEmpty) {
        _filteredOldTestamentBooks = _oldTestamentBooks;
        _filteredNewTestamentBooks = _newTestamentBooks;
      } else {
        _filteredOldTestamentBooks = _oldTestamentBooks
            .where((book) => book.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
        _filteredNewTestamentBooks = _newTestamentBooks
            .where((book) => book.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    _onSearchChanged('');
  }

  Future<void> _loadUserData() async {
    if (widget.userService != null) {
      try {
        final user = await widget.userService!.getCurrentUser();
        setState(() {
          _currentUser = user;
        });
      } catch (e) {
        // Silently fail if user service is not available
      }
    }
  }

  Future<void> _loadProgressData() async {
    if (widget.progressService != null && _currentUser != null) {
      try {
        final allProgress = widget.progressService!.getAllProgress()
            .where((p) => p.userId == _currentUser!.id)
            .toList();

        // Count verses practiced per book
        final bookCounts = <int, int>{};
        for (final progress in allProgress) {
          // Extract book ID from verse ID (format: bible_bookId_chapter_verse)
          final parts = progress.verseId.split('_');
          if (parts.length >= 2) {
            final bookId = int.tryParse(parts[1]);
            if (bookId != null) {
              bookCounts[bookId] = (bookCounts[bookId] ?? 0) + 1;
            }
          }
        }

        setState(() {
          _bookProgressCounts = bookCounts;
        });
      } catch (e) {
        // Silently fail if progress service is not available
      }
    }
  }

  String _getBookCategory(BibleBook book) {
    if (book.isOldTestament) {
      if (book.id <= 5) return 'Law';
      if (book.id <= 17) return 'History';
      if (book.id <= 22) return 'Poetry';
      if (book.id <= 27) return 'Major Prophets';
      return 'Minor Prophets';
    } else {
      if (book.id <= 43) return 'Gospels';
      if (book.id == 44) return 'History';
      if (book.id <= 56) return 'Paul\'s Letters';
      if (book.id <= 65) return 'General Letters';
      return 'Prophecy';
    }
  }

  Color _getCategoryColor(String category, ThemeData theme) {
    switch (category) {
      case 'Law':
        return Colors.purple.withValues(alpha: 0.15);
      case 'History':
        return Colors.blue.withValues(alpha: 0.15);
      case 'Poetry':
        return Colors.pink.withValues(alpha: 0.15);
      case 'Major Prophets':
        return Colors.orange.withValues(alpha: 0.15);
      case 'Minor Prophets':
        return Colors.amber.withValues(alpha: 0.15);
      case 'Gospels':
        return Colors.green.withValues(alpha: 0.15);
      case 'Paul\'s Letters':
        return Colors.indigo.withValues(alpha: 0.15);
      case 'General Letters':
        return Colors.teal.withValues(alpha: 0.15);
      case 'Prophecy':
        return Colors.red.withValues(alpha: 0.15);
      default:
        return theme.colorScheme.primaryContainer.withValues(alpha: 0.3);
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Law':
        return Icons.gavel;
      case 'History':
        return Icons.history_edu;
      case 'Poetry':
        return Icons.music_note;
      case 'Major Prophets':
      case 'Minor Prophets':
        return Icons.campaign;
      case 'Gospels':
        return Icons.auto_stories;
      case 'Paul\'s Letters':
      case 'General Letters':
        return Icons.mail;
      case 'Prophecy':
        return Icons.visibility;
      default:
        return Icons.book;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        leading: Semantics(
          label: 'Go back to previous screen',
          child: IconButton(
            icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
            onPressed: () => Navigator.pop(context),
            tooltip: 'Go back',
          ),
        ),
        title: _isSearching
          ? Semantics(
              label: 'Search Bible books',
              hint: 'Type to search for Bible books by name',
              child: TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: 'Search books...',
                  border: InputBorder.none,
                  hintStyle: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                onChanged: _onSearchChanged,
              ),
            )
          : Text('Bible', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        actions: [
          Semantics(
            label: _isSearching ? 'Close search' : 'Search Bible books',
            hint: _isSearching ? 'Tap to close search and return to book list' : 'Tap to search for Bible books',
            child: IconButton(
              icon: Icon(_isSearching ? Icons.close : Icons.search),
              onPressed: () {
                if (_isSearching) {
                  _clearSearch();
                } else {
                  setState(() => _isSearching = true);
                }
              },
              tooltip: _isSearching ? 'Close search' : 'Search books',
            ),
          ),
        ],
        bottom: _isSearching ? null : TabBar(
          controller: _tabController,
          indicatorColor: theme.colorScheme.primary,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          labelStyle: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          unselectedLabelStyle: theme.textTheme.titleSmall,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'Old Testament'),
            Tab(text: 'New Testament'),
          ],
        ),
      ),
      body: _isLoading
          ? _buildEnhancedLoadingState(theme)
          : _error != null
              ? _buildEnhancedErrorState(theme)
              : _isSearching
                  ? _buildSearchResults()
                  : Column(
                      children: [
                        if (_currentUser != null && widget.progressService != null)
                          _buildProgressOverview(),
                        Expanded(
                          child: TabBarView(
                            controller: _tabController,
                            children: [
                              _buildBooksList(_filteredOldTestamentBooks),
                              _buildBooksList(_filteredNewTestamentBooks),
                            ],
                          ),
                        ),
                      ],
                    ),
    );
  }

  Widget _buildEnhancedLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated loading indicator
          SizedBox(
            width: 80,
            height: 80,
            child: Stack(
              children: [
                CircularProgressIndicator(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  strokeWidth: 8,
                ),
                Positioned.fill(
                  child: CircularProgressIndicator(
                    color: theme.colorScheme.primary,
                    strokeWidth: 4,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          // Loading text with animation
          Text(
            'Loading Bible database...',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'This may take a moment on first launch',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          // Progress steps
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
            ),
            child: Column(
              children: [
                _buildLoadingStep(theme, Icons.download, 'Downloading Bible data', true),
                const SizedBox(height: AppSpacing.sm),
                _buildLoadingStep(theme, Icons.storage, 'Setting up database', false),
                const SizedBox(height: AppSpacing.sm),
                _buildLoadingStep(theme, Icons.check, 'Ready to use', false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingStep(ThemeData theme, IconData icon, String text, bool isActive) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isActive
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 14,
            color: isActive
              ? theme.colorScheme.onPrimary
              : theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Text(
          text,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isActive
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedErrorState(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                Icons.error_outline,
                size: 40,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'Unable to load Bible books',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              _error!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.xl),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                OutlinedButton.icon(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Go Back'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.lg,
                      vertical: AppSpacing.md,
                    ),
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                ElevatedButton.icon(
                  onPressed: _loadBooks,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Try Again'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.lg,
                      vertical: AppSpacing.md,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressOverview() {
    final theme = Theme.of(context);
    final allProgress = widget.progressService!.getAllProgress()
        .where((p) => p.userId == _currentUser!.id)
        .toList();
    final totalBooksWithProgress = _bookProgressCounts.keys.length;
    final totalVersesStarted = allProgress.length;
    final masteredVerses = allProgress.where((p) => p.isMastered).length;

    return Container(
      margin: const EdgeInsets.all(AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Progress',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  theme,
                  Icons.menu_book,
                  'Books Started',
                  '$totalBooksWithProgress/66',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  Icons.auto_stories,
                  'Verses Started',
                  '$totalVersesStarted',
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  theme,
                  Icons.check_circle,
                  'Mastered',
                  '$masteredVerses',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(ThemeData theme, IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.labelSmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    final theme = Theme.of(context);
    final allFilteredBooks = [..._filteredOldTestamentBooks, ..._filteredNewTestamentBooks];

    if (_searchQuery.isNotEmpty && allFilteredBooks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              'No books found',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Try searching for a different book name',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: allFilteredBooks.length,
      itemBuilder: (context, index) {
        final book = allFilteredBooks[index];
        return _buildEnhancedBookCard(book, theme);
      },
    );
  }

  Widget _buildBooksList(List<BibleBook> books) {
    final theme = Theme.of(context);
    return RefreshIndicator(
      onRefresh: () async {
        await _loadBooks();
        await _loadProgressData();
      },
      color: theme.colorScheme.primary,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppSpacing.md),
        itemCount: books.length,
        itemBuilder: (context, index) {
          final book = books[index];
          return _buildEnhancedBookCard(book, theme);
        },
      ),
    );
  }

  Widget _buildEnhancedBookCard(BibleBook book, ThemeData theme) {
    final category = _getBookCategory(book);
    final categoryColor = _getCategoryColor(category, theme);
    final categoryIcon = _getCategoryIcon(category);
    final progressCount = _bookProgressCounts[book.id] ?? 0;
    final hasProgress = progressCount > 0;

    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.md),
      child: Semantics(
        label: '${book.name}, book ${book.id}, $category category, ${book.isOldTestament ? 'Old Testament' : 'New Testament'}',
        hint: 'Tap to view chapters in ${book.name}',
        button: true,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _navigateToChapters(book),
            borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
            child: Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Book number and category icon
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: categoryColor,
                    borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Text(
                          '${book.id}',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: Icon(
                          categoryIcon,
                          size: 14,
                          color: theme.colorScheme.primary.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                // Book info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        book.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.xs,
                            ),
                            decoration: BoxDecoration(
                              color: categoryColor,
                              borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                            ),
                            child: Text(
                              category,
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Text(
                            book.isOldTestament ? 'OT' : 'NT',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                          if (hasProgress) ...[
                            const SizedBox(width: AppSpacing.sm),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm,
                                vertical: AppSpacing.xs,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.secondary.withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    size: 12,
                                    color: theme.colorScheme.secondary,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    '$progressCount',
                                    style: theme.textTheme.labelSmall?.copyWith(
                                      color: theme.colorScheme.secondary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                // Arrow icon
                Icon(
                  Icons.chevron_right,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ),
      ),
    );
  }
}
