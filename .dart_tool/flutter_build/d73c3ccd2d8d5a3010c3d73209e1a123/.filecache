{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "7146d1c18ac515c3fd3465cd4a7f7a34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/firebase_options.dart", "hash": "df28ae2c4812ff5a3e9f41ca219b92ee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "bca928191c274201a95a3b9474582b31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "a85856ccbb262dd4c1207418f8bc7801"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.59/lib/src/interop_shimmer.dart", "hash": "b6d804ca88cfe7ef727b6da2d7c35b30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/pigeon/messages.pigeon.dart", "hash": "132f7087bbe5c2e02305040af52396f8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "aad5ba4c4076b74ded1d769dc1edbceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_y.dart", "hash": "853e7e8b3898f3c0055ae0ae1630e229"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "30d771880c8dbd68ea8e5d4a55c778c5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "134441e2b4b42a7b2ee012ce48910557"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "ceca25b48ef58dff53262c111c0dc9e7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "9be021a3c68f7ef171b79893e7b4fcd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "229f98ffbc538c9813ef41d9f707f00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/LICENSE", "hash": "94016e710124ccd8afa685f270d396c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/platform_interface/platform_interface_recaptcha_verifier_factory.dart", "hash": "cbafdcf0e19b051fe5c2f18e5dd5887d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_field_value.dart", "hash": "9b3531d5f8627f9ed5d61effd19122da"}, {"path": "/Users/<USER>/flutter/bin/cache/engine.stamp", "hash": "77d5cf2c86b02915e4bccde210df7698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/timestamp.dart", "hash": "816a4c75cb3f6727da7137960ee9826c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "2aacf74fb08ed144ee859c99233588ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.dart", "hash": "c6f78ebc1239a030ffc141df9b33aed1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "200da5ba0b0cee2bca1acd1c4d772118"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "d4335eeb3dd8ee5df4498661b368ebea"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "ed59d68fc74e5f7be21e0d7fc1c7242a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_l.dart", "hash": "90a6d35e7a7db7adff31af7c8aeb6182"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "fa8eb6909c6c4c0ced2ac0ec5a69f640"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "30c8c6264748aba97477a1c81c8fb9d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/sphere.dart", "hash": "ff5d66c50ec833a263625d39f0c195b9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "6f6fb24055973d0370e30a78ca69db89"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "ad4f49532706bd4252a8383731d0e349"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/snapshot_metadata.dart", "hash": "57fb1c600d88cf23035d1d7e8086e5e5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_w.dart", "hash": "36e5b08967f3abd15930bde25e9d2ccb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "2a10c15764942d10992468122feea62f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/file_io_desktop_and_mobile.dart", "hash": "a2f208880d92532a9d975bee2451eee6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "527d9250e523e442bc07faadf2cb1741"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "04ad97adf4dc5676764aa8d7aad857f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/progress_service.dart", "hash": "ff1e61848c23d39c7ec6cbae9635ce89"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "21494fec4563fdcefa3d28fad8ffd12b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "702f8b87ec7fc125312d9ff64434e7cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_n.dart", "hash": "8fe95cebce3f522e41f0bef51a1818b7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/load_bundle_task_state.dart", "hash": "5ebaac629645187453b3392f67371587"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_g.dart", "hash": "edafd82e0b999bc51b79c8a3561ff1eb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/about.dart", "hash": "1a8cf97475fa611bd193041415e8220f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/lib/src/firebase_app.dart", "hash": "fc8837c1b0c22211799e9412e64b08a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_aggregate_query_snapshot.dart", "hash": "cbc3b9ead81b6f1f3eca3e40ff353c48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "15a20afe634cea8448869b051ad52b3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "2d18e0064b57f514fab5c3abc06ace0e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "377fef989628d5fbcb306e46a03b7a12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/player_mode.dart", "hash": "8cf1f0b9fb7efda426c4dc55239b596f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "f6d7d6477016f1f991e57b2cbeef7292"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_variant.dart", "hash": "ce58628e17748af44a93e252b9c54d1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "57f09243c4e3f4099a10951225c6d1ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "986845a7043505c19753e1d499d49a4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_document_change.dart", "hash": "61d3ae70e4af2aba20f82da2cfd71d70"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "721c2d087f423a3293f5314804ae66a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_document_reference.dart", "hash": "8539958cacf6077959c3b436480847e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "a2eb984b374f7375264ed4b139a0eb03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "hash": "1c52a06a48033bea782314ca692e09cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/platform_interface/platform_interface_firebase.dart", "hash": "81402c8eea37df800d379c88bdcf6f44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/platform_interface/platform_interface_user.dart", "hash": "81f3b33078dc54811a0c7b2cd837dc1b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "fa0d3415d04242864a0c411fceeaabd8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "e4a32acbcd5da5e636d429dc167fc5f2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "53d7a28895126d1b4c472405e2876fb0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "1962877d0f77e2d3d7ebadbb093d4997"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/slider_parts.dart", "hash": "c66e615feaae8abf62893d4eaeef0ed6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_q.dart", "hash": "e126494233cc791fd4f817e26948cb99"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/LICENSE", "hash": "e8b32b6d7c1328dfb1968caef8249452"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/query_snapshot.dart", "hash": "da25cc23f13cc06d08f5f858974ac845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "e4eb87da41119742a2dcbcdbc39c7a96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "5d30df9a71208100cd9e649ec1f21f69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "7c2c3a23031810f7aa97f4d2f016330d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_v.dart", "hash": "4cb87d15a1cc8c482587425775418f04"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_load_bundle_task_snapshot.dart", "hash": "a7b13f433740523e39807af42322f16b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "1fc94d5523beb1dda68dd704b8f99bd8"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "hash": "b585657f29ecc9eaf59bf8e3186d93d3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "289e5bbf4975b43a1bc7510306854b34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "2a08c219491feeb1c8e9b9d492ffce44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/utils/exception.dart", "hash": "f6ce1f4f743b0ff80647308e0741d4ab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "78f6899dd22a8086e573217b5538f98c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "625b4ed63675ca8ffe8c11d0469bdd9f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/audioplayers_platform.dart", "hash": "9bbe8721d5b91f087e8cecfc2f5002ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "7514fc34af698a2ef36a68486f7340d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "7cbeab73e95bd7561ac8b9519c579ffb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "b266a6c412cb5bbd5355fc22a3be3f84"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_web_browser_detection_io.dart", "hash": "632d3c730c9b6e4f46d9c0459c53ca9c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "d161560a01cd02902c87f5decd590cfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/vector4.dart", "hash": "77900a31d721da1722fe34c455a00d3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/cloud_firestore_platform_interface.dart", "hash": "b6e49305ed78e2a1ac59c1c5e4eb5677"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/lib/src/shared_preferences_async_android.dart", "hash": "ea573095608baeef12806604d26138aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/map_extension.dart", "hash": "76b69f6f885b42f135bcc12cf3a88df3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/triangle.dart", "hash": "2083695b7b9150b87307af446032ba45"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "87f0b72f24e05d2d3f4b0f1b4709eb51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "00ec0dfac52c24607bbdffd84060d019"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "3c1bedbe57228c35f8421d813a7237ec"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/Info.plist", "hash": "3e84ffd77b8e1d46de0dec59f05dfec4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "481e435dd11c202a9d2293db5b58b179"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/get_options.dart", "hash": "13009e9142dccad770f96002acbeb849"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "29439c1f30cb2958458664e1e6e40289"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/cupertino.dart", "hash": "21e240878a582ab39a490e6ac330c645"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "904ebe4c195d2036f989a5e1c3c6052d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_m.dart", "hash": "167efb1e5d1b6fa8a22f6454fbf2a9c6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "135373d55120d14b786fdabe98c9c64b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "77d5759abfee21d18803f19b603da875"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "83fc222e671ddaa7fdb3868c0acaba0a"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "e9b0c1f2903ca05a29681459603679c1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_windows-1.0.0+beta.8/LICENSE", "hash": "6217a25f93e8cd70a46d36976630529f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "064a460171599d3d2a4596a5d1ea2b00"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "62e0b7dc1550fd71644c5cc94797eee1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "0d9e952ceaa817539df84d30e876c4ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/field_path.dart", "hash": "2d56133522ade4d2d0b4c8410e440657"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/query_document_snapshot.dart", "hash": "00b08bcd937045b7367cadd17e47e22f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "7dc8dec32ceed4732299990cedf383dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "8fa0c1ec158277156da896110a03d968"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/yahoo_auth.dart", "hash": "2118bfa3c1a8120c588bf2bcff7cd215"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "af5377d18db2f18bd4ac0ec35ed7d308"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/karaoke_word.dart", "hash": "4b2dab8381ecd5bff799203edcd42e5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/global_audio_scope.dart", "hash": "e1acad940b1d061b1562f25cd953ec08"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "2f426329cad3640d8a125303c3509018"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "6efba60755f63ff2efc82c76d3a50222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/src/recaptcha_verifier.dart", "hash": "6f4de76ba696283829419d6bdc1ce922"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/types.dart", "hash": "e71006dea51cd7c4cd02b8a4952b8de1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "5a41dbb4425fcc9ce228f1db68360c1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "95545fdf17c2014df41408bad8115997"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "c93eab1631a5606c8ba301346fa8e483"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_firestore.dart", "hash": "5cca1f192ac224b9b3fe43d892d139d1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "b7b71e22b53d4d100702d2ba7a7130db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/utils/convert_auth_provider.dart", "hash": "22dce65300b90950ec9ef071bc63991b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "6be1e6f404dc5206ea2b4fa512c45dc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/source.dart", "hash": "e7383b8b616a77bd90828660089eeeff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "ef951139f9f55dc5b330d20e15d4fd0e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "bd6d122c12d867a991bd2fd36a3c46a8"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z", "hash": "63088cb1ac331efd4c32a4f8f2781d3f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/firebase_auth_exception.dart", "hash": "f9208412d85f23d4a61dd21aa34b1c3b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "8e286948f2eaa63514196c1e4c91666c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "5c3150272dcfc4b6d488ba16b0b21594"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/auth_service.dart", "hash": "080f516081c9027962d32c5608173a65"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "734e496890e84ac4195229409538f700"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f949f49484067589ef08e13a892f3101"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "56a59615d1fa716ece6eff8304f7bd34"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "0b8f9e0997c003bc97a462a2c70b91ab"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "7c8b701267e773fa9293eb10736e0ca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/src/user_credential.dart", "hash": "cf414f341b919101304481641615eadb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "2150550461fec00b57e9b9110f8fde94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/platform_interface/platform_interface_firebase_plugin.dart", "hash": "07db573490cf88af2c2da7b393436779"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "393c6d8b9c1a038b62a418fadf8c69c9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "af709d56567f1923ade761542e8dd796"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "b7525dbbd1c51211c6edc9ea544a62e4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "d44c6aa2c95d66ec45eeb0bd0df79cee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/utilities.dart", "hash": "121fcbdc1af81a0fd804490f85357fa0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "dc196a3f1d514347c5f7da6e197b384d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/outputs/advanced_file_output.dart", "hash": "fbb6c76614692e2915d8fa88317d832e"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/firestore_service.dart", "hash": "4f82d47fb02a54fecf5827c81388d5a5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "c7627484ec7f4005dae2321f6de6768e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/transaction.dart", "hash": "5043492c258720231cc9c59883748a22"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "75abcdfe5d010a07b1833f1a2c48fa73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_write_batch.dart", "hash": "2d80e72966694c162ed89db4ac8995b6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "8e7e80b0f55481814454154289581855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/sensitive_content.dart", "hash": "f0d920fb2a472e43514830b20d401806"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/write_batch.dart", "hash": "0ea2980789e8877b57bdd382332db235"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "aaf8cbac74b7b5a3a487d5ddfc2bcdbc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "c6da76a71962267cab91aadde5b59426"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/auth_provider.dart", "hash": "bf9a55c75e9a16d6d584931aa34fdc47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "b3eacd047eaec8b4b214d8d35f471f06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/method_channel_multi_factor.dart", "hash": "437dfe83fe72ee84743ca5086f08af8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/constants.dart", "hash": "4a4b67b573e2338cf03cb704b2c18f04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/collection_reference.dart", "hash": "b7307254237daa7fee1743c17e29706c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_aggregate_query.dart", "hash": "8681d8b6f3c765575761602b0e6a3a56"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "9f5e8439ef3cbfa84f76922ec3580363"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "f7bbc690baa3db88e9a15522b9c2f139"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/firebase_auth_multi_factor_exception.dart", "hash": "04e113b53fd1a325921f14600062c375"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/action_code_info.dart", "hash": "55894e7276273bfb51569b7882109c6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/release_mode.dart", "hash": "a22941f0df71fb779ad5cd5aa952a4c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "d3eb6373e2fd626717b8de7cbf19cd8c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "965c702e5f0b6ba27c6292cf3a602781"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "a7424dc75f961325d400c58f0e946ba2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "0e2afa27a9682352d434c10d20ffdc7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sensitive_content.dart", "hash": "8bb5842ab79616954e268adb624dc6fb"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/.dart_tool/package_config.json", "hash": "95bf6255e27d5f166cfb9700d28aba66"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "9e8b56ffe3de97538d012849a1afa5ac"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "28464c209a2293d3d4e5549539e1a751"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_query.dart", "hash": "50ea49db591b3989fae5517f5c8b8e9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "cdef014561140d05b803ce8d9d85e02e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "4fd63b752aa4c209c7c0bdd1ee5f8a10"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "205bb888a773c736206a9f2c84c8fd92"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_k.dart", "hash": "de8b58c147e392ac3e1a5479f4941290"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/bible_books_screen.dart", "hash": "986d50e19c76ea2e06caf29c9128e9c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/audioplayer.dart", "hash": "55a088b5505a01dcc81248610f17e70e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "546a4af6d99fa77922a881e2f131c1f3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "5bbe4c9f8221f331ef61519909f5cc54"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "d9d777d58bfe8521d1cee4c60700de58"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/models/bible_verse.dart", "hash": "31bcc8ff15a0f6d0cbafedaf454d56ed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_collection_reference.dart", "hash": "5e1c4a415d56b8e60f377d7bb944da08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.3.0/lib/speech_recognition_error.g.dart", "hash": "a5df5f31e15cfee3902ff60a60eed5eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/utils/exception.dart", "hash": "fdb67d4699a6d6b6df4f14e3b046bb23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_firestore.dart", "hash": "a857b7209de140eefbba732f67b1689e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "62f852a5f85345e608cdc7b62a689202"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "a6c467b3086118863463a925df22d187"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.59/lib/_flutterfire_internals.dart", "hash": "09004088d4048afe4f54ef5c78ffe98e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "8d05e0330774daca2ab93f307ded78f3"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/music_service.dart", "hash": "b787bba78e04216cd7efa33451699b7f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "3eb1458ae1a271dbe202030d5b8f0852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/global_audioplayers_platform_interface.dart", "hash": "0d5408aaae99dbdabc0b7871b1d33d21"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "89b2bd5c8fc199b582eb9f10973f97b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/vector_value.dart", "hash": "5ce917fbabbc1c58f6a372a2bfd5062b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/expansible.dart", "hash": "e9a141d0ed4d585b165b7fcacc3874d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "391dfdeb37052a0c52eb8adbc96bffc1"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/verse_service.dart", "hash": "202695448490df019e20a49eaf1bde12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/email_auth.dart", "hash": "4204a4b4889d4553c7cdb1a7e4f94f1e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/raw_radio.dart", "hash": "4d6c8c8185327af9d064a1fbeab18fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/colors.dart", "hash": "f3747e025d835d0ff5cfd904d925dea2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/lib/speech_to_text_platform_interface.dart", "hash": "c3222af7cfe52a6fee04d1bcbbf52bc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_a.dart", "hash": "6f31150716f793ef18c1216f785c7e6e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "cc6cce102fab186d0e7a063d0d917504"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "325ce403b3634a9c45bd705d91ed31a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "hash": "693635b5258fe5f1cda720cf224f158c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/vector_math_64.dart", "hash": "95bedb83cd5b163e43b554086b016380"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "ea7754f2c684266799d36538300b6ffa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "ab7af0d1396dfa5930adaf0357fdc1cd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/material.dart", "hash": "79c87aaef3dd490ff1c43fad2f2f6e8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "b0851d75151b4ad4d87a1443d2041382"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "9f069b0f65439fc693626369d779c95e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "0c9897499d9ef356aa9886423cdf96e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-7.0.1/LICENSE", "hash": "387ff7f9f31f23c3cf5b17f261a091bc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "00c9e1f53ab22efcb34cca55fc46b4cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "dc667b5b278c7b8a2191913ac49e33d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_family_with_variant.dart", "hash": "1562c4a8bfee3d68c041674517ef436c"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/audio_service.dart", "hash": "b345fcaab9bc0f0c84ddd0d20682232f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "b4ab536e0cb6945296bb962bc1e9a3f2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/page.dart", "hash": "c5bf16620e9021a14d7fdd8d605e611a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "a3bdbf775c61477db47c508f513688e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/firebase_auth.dart", "hash": "b321bb570ac5915a3435cbf8a5ae329e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "50062b12181ce59a75a26727cacaf5cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/allowed_keys_helpers.dart", "hash": "41696c18e708950dccaf7cb4f5b7b195"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "92868012710ac163590ba05c788c0816"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/home_tab.dart", "hash": "996514bee23c56c90f1780ce9952e2f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/lib/src/shared_preferences_android.dart", "hash": "2750068c2477624c5ea84c2cb4b1a531"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_windows-1.0.0+beta.8/lib/speech_to_text_windows.dart", "hash": "a929730b53383c1207e3d75c57a45ea0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "154bcb3658f38871192c3955ebccb00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/models/verse.dart", "hash": "9c901b90d3df53f104d74ae4d510fa03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-4.2.1/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "f45f530a8be1596d7ffd25719c66c87e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_enum.dart", "hash": "4817a73df1c313cf6a6eb86774e7fc99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/firebase_core_platform_interface.dart", "hash": "a12b9a0771829ebdd5571928f9c48e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/set_options.dart", "hash": "e5520e4c07409e7057774461188a44a2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "19c24981d3d862f7206e587073eaae67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "0864ad73108959b573b007ab6025d731"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2b2a74f1e45f48fed04eab35ae3c85d7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "7c12bdd660d493a20f3d692be2cafe20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/lib/firebase_core.dart", "hash": "e80d9a4901b1381733c442e0cc05a708"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/firebase_options.dart", "hash": "e6d46ef26f51e2159a2d4cff5e3e473c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_x.dart", "hash": "8c0609f71af975bf4d5197e6e0352a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/uri_ext.dart", "hash": "e3a5b52aab7b3ca32048bc0fc0c3b3e8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "5b92fc2fdb9b39ca8d3072d08f9f2356"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "58feb628edda8670acd9b4c4db589918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "aff8f09b64bc316bf514d7a58be4131f"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "b9127267cdd2de6c1285a11eac48d269"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "3fab1c4c90dce6d5451027be460e81fc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "a2350d9426fefa6d657868d9e59eac7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.3.0/LICENSE", "hash": "94016e710124ccd8afa685f270d396c2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "9b61320422b3f577a77f50badebd040f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/lib/src/messages.g.dart", "hash": "f328303019cd4d42a129d5440c911f8b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "62dce337eb5905e15da1113e7ba50806"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/field_value.dart", "hash": "d67042d4539d36b6fe4eda629a8834b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/method_channel/method_channel_firebase_app.dart", "hash": "4f4575a514eec25990a9923547e2ac28"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "eaef2926557480e27a3ce92f89de68b7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "b78c67723942ac5480c158576c1247e3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "d74d8acd1490e1db907df61d756d2c71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/firebase_exception.dart", "hash": "7cb7fe22378ec39b40d4b519d0928d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "5aa51467523e637443dec44f6c7b1e6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_base.dart", "hash": "ba78ae31f8b033543921d261bbe60dca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/aggregate_query.dart", "hash": "8eacb277b73d0cbe79a6a91b44de25f3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "61dd7991c06ba3bae351fee9a80c64e1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "4a73924a7083f5e9d700ada6f2b53098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/progress_screen.dart", "hash": "85afa1a8e99d70ed7c5f8a956c9a5a1e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.10/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_query.dart", "hash": "6e17644aa952997f9fdf556249e71438"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/synchronized.dart", "hash": "044e7c8ac3258945fe17e90e1a4fff51"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/.dart_tool/flutter_build/d73c3ccd2d8d5a3010c3d73209e1a123/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "27c61344ce9c31ab29dff9add7511263"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "63ea4f418d2305e0cf2c18a773821f9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "0b3ae865c8e82bcd0c94aa60cdd8237f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_index_definitions.dart", "hash": "8f3cb9e3888b2ea58279679e01698147"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "998487b87817cbb87019455d4abfaed8"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/targets/darwin.dart", "hash": "8e0517e25fde2e092795b066ba2b7310"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/aabb3.dart", "hash": "b6a30b7ed48f83f446db37577b30e62e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "0f70aaa46e42cb439dcc5a21fba00f44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/method_channel_extension.dart", "hash": "dabf4184748562b676afcfe8590a3f18"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "8fde18d2ef5c741e3b748bbc854d6b17"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "d3ac4a3d093bab7e3c97e51db9e4218f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "a36981329a77de46168efd089c4102e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/method_channel/method_channel_firebase.dart", "hash": "1c59332e69e34c4a84aa48efd657f103"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "4da7ecc08c07abdd0226004f30973748"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cccaa1a390453623404ad2f98ba719c9"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/utils/app_logger.dart", "hash": "392645eaf8096fe0e6a0aa3897477779"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c390764eafafcc20c2e51225ce144ba8"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/practice_tab.dart", "hash": "f26531603c985b7b02d248f6de64eaf5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/error_helpers.dart", "hash": "c83781cf0c38883486f707cddbb96773"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/global_audio_event.dart", "hash": "09d68e4aa3fd97ade161dbacaf50c4d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "1357b049a06aa8a7413982e814b87ab5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "838c8a1a376a7c9c3fb3424927bcc75e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "984acd55714db5ebfdcab5aeb55467fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/carousel_theme.dart", "hash": "6a9dc1f0e0e14fc0ef5efb4c3c1e8a77"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/firebase_core_exceptions.dart", "hash": "bc949707cfd60ff573b48a27b02f6756"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_write_batch.dart", "hash": "7d4d7122ce259b7b85f91cd30f9dd98f"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/main_navigation_screen.dart", "hash": "97a8ed2b3b27520ce7a4c7751dbfb759"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "d74cafcf507b38e3f3094c6d5ed94a9d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "787093e38fffbbd356129a373907124c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/ios/Flutter/AppFrameworkInfo.plist", "hash": "3e84ffd77b8e1d46de0dec59f05dfec4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "4a7b03b0c037b260c1a321f7aaa8b6ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "f3d29b37515ed98685cd81aa319dd254"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "8cff8c004f57019314d3fe8176de4043"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "72b6519b69dfbf0f2959b7e590bea0bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/audio_log_level.dart", "hash": "d724ececf3dd3780321713b59540a719"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "f94061e9a635be75dd8e38eab352c344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.3.0/lib/speech_recognition_error.dart", "hash": "d16cbe643e1b9f1d249d52de66e9e399"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/.dart_tool/flutter_build/d73c3ccd2d8d5a3010c3d73209e1a123/app.dill", "hash": "2b61e844d6cd6971e6d2ee9a2ea87723"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/common/empty_state.dart", "hash": "853392137e1c9d31f0bc47758f1346db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/oauth.dart", "hash": "8580906f87edc69c5a6b5bdaf82e775f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/expansion_tile.dart", "hash": "3188cef277d7af7b79cfeb3286289551"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "7e45468116224ee318aa9b1f210cab12"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "1e0f99d28825c416ceb5f264b6af7fdc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/position_updater.dart", "hash": "6ef244fb76fff28e2cc87f7de3f9a8e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/method_channel_user_credential.dart", "hash": "eaf53330c178d6d2fe4f66f2bcfbc00a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/audioplayers.dart", "hash": "ba0b67d74653751897f272e381f2ceb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "74c42b320d58fca1c02c22c577c5fdf7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "66df4fe41752a6a990878623e36a3ad2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "c7fe678fd3ad24ff5928e24dff4367b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/lib/src/firebase.dart", "hash": "5ac6d992f5cbebe5e5d4e8bc4ed5ae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/method_channel_user.dart", "hash": "d4388560402246b7f8409337dfd6c1b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "a88e90675c4b55522b3e9226f0135237"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "e76d7da2d8f4281119d176fdcc04b991"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "cf63ef7fb2873f43a2b2e25485734429"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_platform_interface-2.3.0/lib/method_channel_speech_to_text.dart", "hash": "d006dde8fd90189f533f571d02092a92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "816a5cf300a6461fe2e7e8ca8a66a709"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_transaction.dart", "hash": "a04c7c0d96e578b4f3ff68960efe3dd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.12/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "5577ef7cd41e467cc247a42b677f93c9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "294ddb67f660c73c07b9ec37562840cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/ios/Runner/Info.plist", "hash": "0c4148240a8e2fb3bdaef4f9f663bbb1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "374ee130942948f52e47681818bd315e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.59/LICENSE", "hash": "e8b32b6d7c1328dfb1968caef8249452"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "a056a48864751b648133bf4d0886134a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "06455706949396049309d1cc90b76efd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/noise.dart", "hash": "14ee798b10cb318d96667b32b245f21f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "b5b9320ef8cd47d81a68063558c1ed4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/asset_manifest.dart", "hash": "604151cdbd54ee0f0f4681fc8840d827"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/google_auth.dart", "hash": "be33dd555b20cb5888b1bf03153b24c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "f64029b4f4dbdc0bc61a4b8787975a94"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/auth_screen.dart", "hash": "eeb6c43eff8a64ee9361969d2b0e9044"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "5176206f3155513053dda23b0c32fc8c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "6edb3eb5d6e5b289f28ce2fb68047e91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/aabb2.dart", "hash": "f54f6b61b175b0a37d51ff3ac8b8c800"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.3.0/lib/speech_recognition_result.dart", "hash": "5701f1a2ae54ccdf11a84a2a6239861e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "5b98d0be4d89f1274c832a4c340ab315"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/load_bundle_task.dart", "hash": "ad5e688583cb347b984e82bf97b2b5c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.59/lib/src/exception.dart", "hash": "9a74595c2e95795b6c96d74f2b6bcca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "db4d348cc51cfecc2c86a34122b48806"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "4c5df57cfe2a6a2bc0d7462330344982"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/frustum.dart", "hash": "fb2be6f27b32bb1ab12dd6aea8c5ecda"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "31d8245447d51dba20c81f00b214fb36"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/profile_tab.dart", "hash": "c2f85dd4dd7ca3a32f44110a66b7a761"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "a73883c523a61b1393b5e8c66de884c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/matrix4.dart", "hash": "ae36c7cc9b21f98bedf401f2d67a0fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "3fce8e0c4d9b3cb4e3dbc168f41a132e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "a0936682931bc884c5052e9f49bf8829"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/settings_screen.dart", "hash": "7a07354a30850090a80e77e581bbecf3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/document_snapshot.dart", "hash": "d69f76e074bb8e230a4e367a1793846b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_converter.dart", "hash": "2d069a48b5e0ffa386474977d2c91c90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "b09ffd962fcbee7d3403b54155e33047"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/ios/Flutter/ephemeral/flutter_lldbinit", "hash": "4c0c8550624ce117572c484ae3e7d9ce"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "91e808d781743242114a756dec8f2cbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/utils/auto_id_generator.dart", "hash": "22eb8e02f17c470130fbc97e05644a5b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/checked_helpers.dart", "hash": "c668a1bfe65f14c115a3294ac6502dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "24cd1bed27dc8cfdc2d00045c1b85b53"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "5ee4b9f196c81041c45d27e3b2d33d88"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "30ff1bba22f8f5d5442537740196fdcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "cd3f0ebbc282b839928f5fe3ad12c779"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "4c13b34211e2b17645a6a5cd8defbe0d"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/pubspec.yaml", "hash": "28e97c70b567eb8079e2f55a86b8628b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/user_service.dart", "hash": "b10fab64a8151230b8472011396f19d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/method_channel_firebase_auth.dart", "hash": "07e42178d7ac10d442c02ddaad500460"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/blob.dart", "hash": "0a974620f3da126bdc552f5364071575"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/saved_tab.dart", "hash": "64f40b21c7d3ee21ad4ddadcd8204188"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "efad3646c2aadca0c462ae31919205ad"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "400da5c3ae6b8c8cf1ad20c796ce413b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/intersection_result.dart", "hash": "866257a42b6b721549b351382b365c47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_load_bundle_task.dart", "hash": "d99ba69bde9981ebc19498552cc818f0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "0ee043f9e3f8fc817bc6bb354731879d"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/ios/Runner/AppDelegate.swift", "hash": "303ca46dbd58544be7b816861d70a27c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "3167bedcdf6eb73bb3355fc778c69ab2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/settings_service.dart", "hash": "9db47284e90cd65de9821367926ad99f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/pedantic-1.11.1/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/audio_logger.dart", "hash": "7dca6d3fe1878e9640ad8d2785605b1a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/utils/codec_utility.dart", "hash": "52caf78a5db7410090f834a9c6b32002"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/flutter_version.dart", "hash": "597d897c972c255ade7307dfcc2e5524"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "028eb8497ffa66b6d051c09361dc19f3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/utils/storage_helper.dart", "hash": "560abb79652528a44929215b86c037a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_persistent_cache_index_manager.dart", "hash": "ca50ddb768a3b597e9af9b6a076bd880"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/platform_interface/platform_interface_user_credential.dart", "hash": "ccdfbaaed6069d0c10b9b88108ef46b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "2663ff02a467c826925672bcaf6bcf66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/load_bundle_task_snapshot.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "a11383c33c4fdc8d2cdc091f50d17e93"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "e1648c3accd2c87d0897e5454a387c3c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "3ce88fe27ca35ed2f5b7a333d43676e1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "de161004250e30098d14049bdf54ce38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "ed11d553b999afddfd85ca57540af7d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_f.dart", "hash": "01acde6ab3416626c8fe453d99c13480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_e.dart", "hash": "012c3b670fb153803ce635838e1fa9ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/user_metadata.dart", "hash": "d50f97a386a7ddfb21486f9755b0b448"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/karaoke_screen.dart", "hash": "4ca0363c0ce9332e5987b4380cb36325"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/audio_pool.dart", "hash": "4a130404f70aec878f16db3ebae6f067"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "8d0306ecedceab52f23b17a0694e7842"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "3dc9f56e0fb2e949ac4c68187162c0a4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "b15a3573191a80dfb78fd6a729390c0e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "648a6cdab2fd44688152ab1b016e5e9c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a74b5a39115ffd608a19cad9309e6a31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "8e0fc402506b32a335e86f7fef97f06e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "ca2e098cce59851623bf60c022a3bad1"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/models/user.dart", "hash": "a976972f35e8ceca2bdfd6b443c99295"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "3d71d8940be022672282ed70f0cbb8c6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_value.dart", "hash": "002be4c072c0cc5c5e72b5ff6d0c490b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_persistent_cache_index_manager.dart", "hash": "0d56f7802ee7b6b5d16ac504e5cbcfd5"}, {"path": "/Users/<USER>/flutter/packages/flutter/LICENSE", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "280be2dbc10de2dd1913281d29e1b29f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_z.dart", "hash": "991a163a470f64b0222de6290e39d538"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/opengl.dart", "hash": "de7fb154b9b151b81a78d43ade695365"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "35bf7179f32e4ab5b13e9d9ec2abbe86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "a1186c224201e7d203404a4270938040"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/json_annotation.dart", "hash": "532a272d043c3dccd91b63d1b428dac9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/lib/src/audio_cache.dart", "hash": "81fa98cc4af6e22387585953adcc541b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/gestures.dart", "hash": "ac772288a52f82606f20d68636327e34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_j.dart", "hash": "998746037e3416b31d33881bf69a4148"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "d857a3ae7f599cc71f41689ffcf1fc5b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "de17df889317f7a54961ea540cf4b807"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "0c32f2e835bc0f32a6b146dd73be8555"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_query_snapshot.dart", "hash": "e5a97009afd6db7405d3da1f9347500e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "61d7b16669f075a39023fed8967fbdb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_field_value.dart", "hash": "2a707b3981377cc615c4f7b3ac4ea8ba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "38570a2af41c2f9a4632e2af3b42ffe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-4.2.1/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "153fd637fe660527ff42e1be068d99ac"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "16f71d097900371eb87d706863a8469c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-5.1.1/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "12580e996c5cb68c4e80588f6dd9f235"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "38861aee0e2ba92ec8005a64746c0d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/settings.dart", "hash": "bcc237a682b7a3629d9ba06723d02406"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "1268762fa54412a0d265cb57a14cba84"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/.dart_tool/flutter_build/d73c3ccd2d8d5a3010c3d73209e1a123/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/microsoft_auth.dart", "hash": "6114182c41ae75920945d8ebcb5f173d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/firestore.dart", "hash": "7cbba232ac648ed078f6baed3c270311"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "40dec7d9dd1c5150bf10ef4b46cc36c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/widgets.dart", "hash": "0d4b8c16e7b8e4d8baf6fca9161c7e56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/LICENSE", "hash": "e8b32b6d7c1328dfb1968caef8249452"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/obb3.dart", "hash": "54c7f23362a7e78be04b113d00022090"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "74939c971de1eb61ef05a7eb5056cc20"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "576f65e88d664b3c39aa0e07825b29a4"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/bible_chapters_screen.dart", "hash": "8572e9d222d0629b5b59a9e43f38d3dd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "cd0db51c646e4809e09bdeb76ec931b7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/App", "hash": "5132b6d75cceaddcc32af4e1b2139e20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/filters.dart", "hash": "9d84f77abe94c934dcd1d6baa9963ba0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "b698617b81ba534ca60cdb6dee762fff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "608be960e670661114e97b498d6a6473"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/github_auth.dart", "hash": "471a11ff82de8d7d474600c0cf12fa0a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "cbbb174cb00bf954fdc9e2854517dbd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/filters/production_filter.dart", "hash": "d455a0ea71515758776153cc65cb1978"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_descriptor.dart", "hash": "df2373fa53c57974996330429774683f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "f7b4c0027cecafcb6711746922663d7c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "2cb2b1aac78bff7cc9be5f0a45aaa94b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "d1d1398bda204825136843ad63735067"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "3cddcab8b952545bc05a5c1475a06c9d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "ce4bfd9659d667457cc3ada513fae71e"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/targets/ios.dart", "hash": "77f6ca8fc03e4edc47032b2817f4f41b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/audio_context_config.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "4250bd72ef305d1f376e96cc6b994778"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "56e9b43aa79d6b888e779ad7905c1617"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/LICENSE", "hash": "2abd2c9a42d4caf2b4f1640d68b02fd5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_c.dart", "hash": "7f1486a2bf169b977f3be1524f930a6e"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/theme.dart", "hash": "3d0eba7547323fe1136f3ba8a4ebef7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_p.dart", "hash": "af493bb7ab298cddebf04d46f7c5dc18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/pigeon/messages.pigeon.dart", "hash": "9ae23519911b78febbb5e8165ed127af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "290ff7e27e670467d4f520e320ed9660"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "69be6215ea4781ec3da1e389b321cad4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/document_change.dart", "hash": "ced7240eff2c32a0289036918f8c7f81"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "9eb1b00e42fadb0be56354c8bc9feb4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_field_value_factory.dart", "hash": "dd3a8675b6902b5897261108fb756d1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/filters.dart", "hash": "873c73ffff21b116ffccc34c53d24cd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/screens/bible_reader_screen.dart", "hash": "d38b04d21cf2dbcd2e51116118e1e78e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "d70df86ce471e8470438627a65b2824b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/audio_context.dart", "hash": "2d156ffcf598f40423e9147a56c2a931"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "f44bbe72c4c7393277c42d5fc27b3b2b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "1b5af29e062854d33f5e4c81c2bdf11c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/quaternion.dart", "hash": "55675ef4bbddffa94d962bd52b3088ca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "9a023a5d9b2c849e9c7fd9e16db1e7e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.15.3/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "64c347128324802ec3aa6618f5723cc2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE", "hash": "39d3054e9c33d4275e9fa1112488b50b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "2af013984ccce4c43e3024da472560d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/utils/load_bundle_task_state.dart", "hash": "219be5ed6a3660bab14a75e949653a6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/src/firebase_auth.dart", "hash": "c03889e7629b8c631d42bbf7d14300f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_s.dart", "hash": "2d79382537f3ba898ab7a80cd0fbf0ce"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "6f02e150859b1047ec04ffa4a924f90a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "79c35fba64a91629072a76526adb9aa7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "2122907e49766bb1f044ae97841c2b86"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "05ab01a88b45fe10a762dc3068e7e1dd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "58678829e383937c51f539f2ad67fc17"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "21a43efc5058f6132660bba47766b26b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "0d8aed1407088c73788f25ffba071cc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "38c93c95cb266619fd6cf7de928884db"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/enum_helpers.dart", "hash": "32c8f2d4dc53cfe56f5fa637be2c52e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/google_fonts.dart", "hash": "885d6001f197c05de34b17e76acc7ed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/vector2.dart", "hash": "81d01d8cecc6783526e350800988db74"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "49f335e51e1a6242ba8ab55b48de9d92"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "85b908f2e50b980d5cab7f458371f430"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/ray.dart", "hash": "d69cd05d9de1731242d357de56893a6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_transaction.dart", "hash": "07969d139f0726717bdfcec4ab7dbd21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/platform_interface/platform_interface_firebase_auth.dart", "hash": "bdf8f330b4541269fcc615a69ccaf00d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "4c09fb1ea4651f47d1a0a67ba3b31886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_u.dart", "hash": "de4ba796e7c200bdc07306e8b82e1f5a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "52f779d8f66642da5db6810754b0ba5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/lib/src/platform_interface/platform_interface_firebase_app.dart", "hash": "209399f0e6f16675c3f087b8eb17087b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/common/loading_state.dart", "hash": "87b90739f30911173b8483fb80a8a22c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/quad.dart", "hash": "25dd0d36ba8109e3199faf508b41d633"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_aggregate_query.dart", "hash": "26978fce525087357219fe9091a5ecf0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/platform_interface/platform_interface_multi_factor.dart", "hash": "11a850c4bde49bf9c0a07bfa98766e9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "92901585628d81f7bb3d578fd6d6657d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/plane.dart", "hash": "d98495bcbc301290a10e6d1dfc255d69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "600a92f02eb307032e6cedc6c5f104f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.3.0/lib/speech_recognition_result.g.dart", "hash": "735445bf95b7dd825aa15c783a4412f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/log_level.dart", "hash": "4c243a6ca83ee01bb17db0d0a77c681f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/method_channel/utils/pigeon_helper.dart", "hash": "22abc41c95a9d0f3a266a18a5e6051d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/action_code_settings.dart", "hash": "3627ec64c2a02093e9ebc7765b0dd911"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "1e2afd780c32baef8cedd0eb9c4dee6d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "5c9195780e56985cc88956aab0887ab3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/auth_credential.dart", "hash": "4396aa6dcbf695d2171478fed7e32d77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/src/multi_factor.dart", "hash": "06d16c6ab5f18201c197169515882d1c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_key.dart", "hash": "6a18f9347e6e639ebbbfb0a0ce98bb71"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "3e0eaeb97804d1bc93e6c6088aa351b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/vector3.dart", "hash": "d4252f423175e5c21fca23dc24154b84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "42212bb3508502e1b011bd783d51ea78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/document_reference.dart", "hash": "29196c4e454b144c75a5c262103bcf08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/aggregate_query_snapshot.dart", "hash": "53b20db612bddbe3c35a9991ab722314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.2/lib/src/port_mapping.dart", "hash": "d1870b4415ddf7f379e6e41b520ca299"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/Flutter.framework/Flutter", "hash": "871b08a29f233339a9be86fc68b4b347"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/printers/pretty_printer.dart", "hash": "04e815c541b96bad075aa0f6e253c662"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/firebase_auth_platform_interface.dart", "hash": "2b8c05869ea5309e4986094390b0404a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_document_change.dart", "hash": "4b784cc5e2e687f8a485e510e4d700dd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "b45f6f4ad67efa5c374cabc278ede26a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/range_slider_parts.dart", "hash": "3d925b9cf0a12dd519256aa23a4e3512"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "**************************a5fca0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "7db055846295bfe7d5e376765ab0d106"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/audio_event.dart", "hash": "581ba1fe013fc429fb064283de6ce94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "9d437a8fcd0a5c0ad90aa6e31d66834c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/twitter_auth.dart", "hash": "597760d6635d34d850cf6763a342628d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "3431f50e7abf9e27af232de10193931a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/models/user_progress.dart", "hash": "91927d7f7be3d984abb9f49a76a35d0c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "1506ba940aec506086f3093420336467"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "3ed378957409718f644078da99891428"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "929b14e4278022b2254952112ac8b9f6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "a47062dc6143c80c485bcfc7a06b9490"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/id_token_result.dart", "hash": "01b342498915efec01ca74994deb72fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search.dart", "hash": "3798784648f57e129514c1cb6f534612"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/facebook_auth.dart", "hash": "2db781259be23ab14c8241d3da04a173"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "d0911329ae74edbd7f6ad6a89e0703f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_o.dart", "hash": "d14d602c73240e571385abe6192469f4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "bf365ded028510087ed69c227bda0d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/platform_interface/platform_interface_confirmation_result.dart", "hash": "44ea3e94a0a33200893a98cfc5448195"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "d828c4334e98a12974d90e38d48db9f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/matrix3.dart", "hash": "7711f4b6c3574cec77169f2d2c35ee3d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "06078529e4523830f3ad70e0aab603d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "c7fd5a3a7f809d37cfe6af2af573d097"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "14acfddcb9e62f0de6f82d28e22c22f0"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/services/bible_service.dart", "hash": "e3f50efc0cb272e313156efeec6782d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_collection_reference.dart", "hash": "b40bd8cd4fc9802a51ee3f61abfb1d14"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "210d4d542d997e93c121b4dc814b95cf"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/cloud_firestore.dart", "hash": "ca3857d448b18d4ee90f461e19b9b755"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/utils.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_literal.dart", "hash": "8388d5e13155ebde873438c26dc4ca33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "63db75c602690371aef0f83279a929d7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "af4bf4aa827f5ac651aed6fb7b9a038e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-11.0.2/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "hash": "092362603d55c20cda672457571f6483"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "b3a6dd250e09b61bffbc04a767f0c920"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.6.1/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "73d5607bd6f5dccf91add39e25ad157d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/models/bible_book.dart", "hash": "81a312dce16c256874d4f2d5b7ef8a4c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "36bb3dc8435f5085b78c2972f8efe90d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_i.dart", "hash": "706f1120f2aad4e908056a2b4f16eb23"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "ab177cf671fb7bab974d9c08618a677c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/services.dart", "hash": "29ae1507a6ec4c2ffae469a10e505bda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "fda1d4b1be4a584133638117945d3dff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "617fb0bcef7162a860ca76636507117f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "1e30703fc6d5663dea611a3c783b21aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.6/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/additional_user_info.dart", "hash": "7d21bcd64cc8769b97f2fe328bbf20c4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/flutter/packages/flutter_tools/lib/src/build_system/tools/shader_compiler.dart", "hash": "57b58eaf8484f5be3274077f5af029f8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "605dcc1d6bd5023fc0b651a625076ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/src/confirmation_result.dart", "hash": "05a5e51b89d4a4db5444886ebd7922ff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "1d8fa1cee64f2d791002749fabe23e2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "18ad2d48b68dc9b514fde418b7acb599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/snapshot_metadata.dart", "hash": "875dd5da6754c8cdea09e318c5f1894e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/apple_auth.dart", "hash": "590a1d4faa1181de2cf4638285b5c04e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.2/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/lock_extension.dart", "hash": "92197f660f809dbb94c7d3d67b9f24e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/audio_controls.dart", "hash": "647f7ff29f9200eb9650084f002e8315"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "83bb40406ac73bcd194c621137ed0349"}, {"path": "/Users/<USER>/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "4935fd96677780d631f23a75e7009534"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "d1e0e0c2904bd9e5145d919296eeb580"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-7.3.0/lib/speech_to_text.dart", "hash": "9b43e915daf4ff9e2b753b1a5b63f8ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-5.2.1/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/lib/src/messages_async.g.dart", "hash": "02e77df022e1321c518555aa2eb3d95b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "65a04fd24f938030b7271b61a59f9a39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "21f4467f19bac7f0fe6f0e730ab10fda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_d.dart", "hash": "2a101a9f7dc3955fa1a1cb93fde33565"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "16d2669eba65e0d92613a0aef0a169d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/user_info.dart", "hash": "6b7412a8082f81abf873d0a8b1ce3fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/audioplayers_platform_interface.dart", "hash": "69233935dacf684f216f33919244a83b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "89b2eba11b385c32cad8745bfba9798b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/internal/pointer.dart", "hash": "e484b66afb812dc26ccd7295cc06ceae"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "9bc30281f42d8003b7f9d636ebc8bfc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/saml_auth.dart", "hash": "5efabebc5d98a8f02bfaf723df1243e8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/material.dart", "hash": "5c93305f52983c3f2be825bf54ebbd78"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/api/player_state.dart", "hash": "347f290ce95265bd445d1dbb55661270"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "34c5e6ba4664d331c977bdc010aad709"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "d67712d7f3394870d88650dc0baf5855"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/foundation.dart", "hash": "84939e70d6b7b36e8098dd0cda8cbb2a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "026b1fa8f1d7ff0d7c1a6e1afb2e75ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_t.dart", "hash": "8f142b64056bff3425661bf170728f45"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/design_system/app_spacing.dart", "hash": "d9668499e5d1e7dc791b3465d2db91e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/play_games_auth.dart", "hash": "c108a832b6c7b8b1f18046c4fdbb7bef"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "2ca785b09f831ebde51eca8654fd23b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "c4d13715583d2c97acba184a3e821151"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/matrix2.dart", "hash": "945227f3863339e388d92c2b3bfbf673"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/utils/firestore_message_codec.dart", "hash": "8608a505057b344f5e502734e6a90c0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_load_bundle_task.dart", "hash": "4193bfde8de2f5661aa59d72e5b0de65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "b39287c180e3ac3047fc5dba3a44a524"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/global_audioplayers_platform.dart", "hash": "bc437d21096c3a26c66ee2c45f40d9f2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.1/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "8dfd28d2164bbd446b480491aace196c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "21496c39aba7bb1435e82558fc3dc9f4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown_menu_form_field.dart", "hash": "6b3b758749ea0e06a43533073febcb66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "a6ce313fc162c7c4402e1979454559a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "484481ff93d08a930ecfcf6907acf691"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/.dart_tool/flutter_build/d73c3ccd2d8d5a3010c3d73209e1a123/App.framework/App", "hash": "ddeabc02d0727cba069ce8874344468d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.7.0/lib/src/user.dart", "hash": "f27954ddd875c82fce3d910910edcaa8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "4ccdd5e6210285f9baf09909e7d4f593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "hash": "2efbb41d7877d10aac9d091f58ccd7b9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "20051c4912af535e0a8362fb1e93f423"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "bbc54fca40953c4a17c12bf45c349c77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.13/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_document_snapshot.dart", "hash": "b71b77637dff6c6c8a2a9b69b6d9a38e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "1542c76e7b3e366d393fcb2c3bc601d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "fc5d931b0e52f2fbd5ba118ca7f34467"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "e14417c43b6cb787f11bebd1c39280cc"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "4d43f0629755f06d4df0b1a6ef75ef59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_h.dart", "hash": "fcfe1d3dbdb081cdeca153aebf6667ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_b.dart", "hash": "bf5efe9b7f7e8bdc46aa542818534985"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/persistence_settings.dart", "hash": "df98d5f947d4e048c8cf0b6553bb4e12"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "997f4b4e6bf9981e307f46f08fa90b82"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "cb49e0d1c096a600c37190f5a40cbecb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "9c23e23bd2cb8afe39b51de3545ab2ec"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/common/app_buttons.dart", "hash": "922f0d8ac40005119cd2326c313d2aff"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "926a78bbb0d20acd22028c14ca8b8071"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "9ffd4af5e11781c62ed4e40fdf15b182"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "a2c7734430a38c6f25a3e99f10aa19fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/method_channel/method_channel_document_reference.dart", "hash": "bf954f32a0342e05c7953dfbb77d2d16"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/app.dart", "hash": "5b539c57fb0cbea66a99efbc8239e590"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.2/LICENSE", "hash": "46158b74167f78e44896e35a92c7c5e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/geo_point.dart", "hash": "4aab5a7c374b4b59887cbce26249d849"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "d891dabfc112fbaa77f11a249d547179"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "90a1a95cfd75677cfe6295f0bad3a3e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/game_center_auth.dart", "hash": "beb6362fbb2d09dafb547b36bf56b936"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "7592e5df71403552b6109cb4fe946eee"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "6618a55cdb528b43addda36642363d96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/reentrant_lock.dart", "hash": "7cff949e3b7ac960b63441117b2f6734"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/providers/phone_auth.dart", "hash": "ab80600b896e316108cea264fbb1653f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/src/json_serializable.g.dart", "hash": "7ff35a1db7f2b80a156d464b075a09f3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "5c3b7996bd913451665c9b1634098d83"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/filters/development_filter.dart", "hash": "a925c024faf2d8bc047793e5a39b95d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "hash": "b8ee00883037778797bf5949230f40a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.2.0/lib/src/vector_math_64/vector.dart", "hash": "1205ed5e14a59c237c712b8a495b1981"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/verse_card.dart", "hash": "269e0ca308ee537d6bbdc9cba9a73cc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "625e266fbab1e46e06c8d7d211a5828e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "fe766313e73046aa145217de64ca7760"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "fe750f835c7dc27ef38ee2fdb486a6ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/persistent_cache_index_manager.dart", "hash": "cacf5786f44f003cc37bd1767a30b361"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "hash": "6cd606d3e368485de4ee213b4887f8a0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/main.dart", "hash": "348ecfdea1831ce4d8b1d73b7f8cf4ca"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "110b9903c2673d2ae6f626dee25c45f2"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "2675cdf47e408031206cc9c215200004"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_field_value_factory.dart", "hash": "e6f21102a864c02377cac5174e624cec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/src/audioplayers_platform_interface.dart", "hash": "a79aa1ec2fbbbfe3318286f6851db272"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "6a612ac4de579506fd1b806fac3fe062"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "24d6b5d55c0b41213c9bb4b2342764f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.2/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "8a60b4ed49f146296d6896973154e1d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib/src/google_fonts_parts/part_r.dart", "hash": "c03845abf8fa02fedbc602853685d92a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "34485853c65233b4daedcede2ade0c69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/radio_group.dart", "hash": "1099a5c5ee8ae0d01e2dd7d07c3edf90"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "0ddbbba088a930cb7ae5b5920ce346cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/field_path_type.dart", "hash": "81eb7325430479047a82a349dec15d5f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/lib/widgets/repeat_word.dart", "hash": "09724b4b07f90fffd922c55595d00a1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.12/lib/src/platform_interface/platform_interface_query_snapshot.dart", "hash": "b5c14811348f382490dad0c2ca257ae4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "7692ca5e3a50523edceb59e80a6205a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.12/lib/src/query.dart", "hash": "b27050765531492da95965039f5ac2f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "2b76d589cc052dc9ef928ddba5382a4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.3/lib/src/pigeon/messages.pigeon.dart", "hash": "97e062089946f432e93112ca36b6b683"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "f01b78dd243cdceae98d62e7429f3d04"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "97c7266e528b6f706b08b4ad340006d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "3e4d53a860279f33b4e7c6b1d9957a51"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "622fb5559ef551a734f0ebae8660485e"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/gestures/gesture_details.dart", "hash": "eafe83db9186e4fbb802d857e4bb42ad"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "0b630cc8a66d79c161a58858593ae1ae"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "ae53c1bc8f95419bee08ba4fde0e173e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "6b6d593e4facdae2c82b9133fa8e69e4"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "e6467427260f3274e8424d691615ca5c"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "ef1ff22066328de1e83b8180592a470f"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "b1da6e4c330ab79eb371fb535a8fb7cd"}, {"path": "/Users/<USER>/Desktop/proj/echoverse/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "hash": "2b61e844d6cd6971e6d2ee9a2ea87723"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "8f77cb7be1dbf41ca0fdf069ac69a215"}]}